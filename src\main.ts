import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";
import store from "@/store/index";
import "@/assets/base/Tailwind.css";
import "@/assets/base/base.css";
import { router } from "@/router";
import Antd from "ant-design-vue";
import "ant-design-vue/dist/reset.css";
import AdaptiveContainer from "@/components/AdaptiveContainer/index.vue";
const app = createApp(App);
app.component("AdaptiveContainer", AdaptiveContainer);
app.use(store).use(router).use(Antd).mount("#app");
