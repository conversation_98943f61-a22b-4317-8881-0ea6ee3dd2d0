<template>
  <div>
    <div
      class="fixed top-0 p-10 text-white w-[25%] h-[100%] flex flex-col justify-between bg-r"
    >
      <div>
        <div class="flex flex-col">
          <div class="flex items-center">
            <img
              src="@/assets/images/<EMAIL>"
              class="w-[404px] h-[56px] mr-3"
            />
          </div>
        </div>

        <div class="mt-10">
          <div class="flex items-center mb-3">
            <img
              src="@/assets/images/装饰@2x.png"
              class="w-[22px] h-[22px] mr-3"
            />
            <span class="text-[12px]">产品介绍</span>
          </div>
          <a-carousel arrows class="w-[280px]" :dots="false">
            <template #prevArrow>
              <div class="custom-slick-arrow" style="left: -30px; z-index: 1">
                <img src="@/assets/images/left.png" class="w-[10px] h-[19px]" />
              </div>
            </template>
            <template #nextArrow>
              <div class="custom-slick-arrow" style="right: -35px">
                <img
                  src="@/assets/images/right.png"
                  class="w-[10px] h-[19px]"
                />
              </div>
            </template>
            <div class="w-[280px] h-[165px] overflow-hidden">
              <img
                src="@/assets/images/banner.jpg"
                class="w-[280px] h-[165px] rounded-[15px]"
              />
            </div>
            <div class="w-[280px] h-[165px] overflow-hidden">
              <img
                src="@/assets/images/banner.jpg"
                class="w-[280px] h-[165px] rounded-[15px]"
              />
            </div>
          </a-carousel>
          <div class="mt-10">
            <p class="text-[14px]">超低压反渗透ULP系列膜元件</p>
            <p class="text-xs">所属分类：工业反渗透膜元件</p>
            <p class="text-xs">产品特点：超低压反渗透ULP系列膜元件</p>
          </div>
        </div>
        <div class="flex flex-col absolute bottom-10 left-10">
          <div class="btn mb-3 btn-active">综合态势</div>
          <div class="btn mb-3">生产孪生</div>
          <div class="btn mb-3">全域管控</div>
        </div>
      </div>
    </div>

    <div
      class="fixed top-0 right-0 p-10 text-white w-[40%] h-[100%] flex flex-col bg-l"
    >
      <div class="flex items-center justify-end">
        <span class="text-[32px] mr-5">{{ nowTime }}</span>
        <div class="flex flex-col ml-6 mr-3">
          <span class="text-xs opacity-[0.8]">{{ nowWeek }}</span>
          <span class="text-xs opacity-[0.8]">{{ nowDate }}</span>
        </div>
        <div class="w-[1px] bg-white h-[30px] mr-5 opacity-[0.5]"></div>
        <img src="@/assets/images/1.png" class="w-[40px] h-[40px]" />

        <div class="flex flex-col mx-6">
          <span class="text-xs opacity-[0.8]">27.70℃</span>
          <span class="text-xs opacity-[0.8]">小雨</span>
        </div>
      </div>
      <a-row :gutter="[16, 16]" class="mt-10">
        <a-col :span="10">
          <div class="box">
            <div class="flex items-center mb-3">
              <img
                src="@/assets/images/标签@2x.png"
                class="w-[22px] h-[22px] mr-3"
              />
              <span class="text-xs">今年主要产品量</span>
            </div>
            <div class="w-full h-[1px] bg-[#C6C6C6] my-3"></div>
            <div class="flex flex-col">
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.1</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">超低压反渗透UILP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[red] mt-2 w-[90%]"></div>
                    <span class="ml-2"> 240 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.2</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">苦咸水反渗透BW系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#FFBF74] mt-2 w-[72%]"></div>
                    <span class="ml-2"> 180 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.3</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">抗污染反渗透FR系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#FFF774] mt-2 w-[68%]"></div>
                    <span class="ml-2"> 173 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.4</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[62%]"></div>
                    <span class="ml-2"> 156 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.5</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[60%]"></div>
                    <span class="ml-2"> 150 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.6</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[52%]"></div>
                    <span class="ml-2"> 120 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.7</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[50%]"></div>
                    <span class="ml-2"> 110 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.8</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[48%]"></div>
                    <span class="ml-2"> 90 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.9</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[38%]"></div>
                    <span class="ml-2"> 80 </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <span class="w-[60px]">NO.10</span>
                <div class="flex flex-col w-full">
                  <span class="opacity-[0.5]">节能型低压反渗透ELP系列膜</span>
                  <div class="flex mt-1 w-full">
                    <div class="h-[3px] bg-[#667474] mt-2 w-[35%]"></div>
                    <span class="ml-2"> 70 </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="box mt-[16px]">
            <div class="flex items-center mb-3">
              <img
                src="@/assets/images/标签@2x.png"
                class="w-[22px] h-[22px] mr-3"
              />
              <span class="text-xs">园区管理</span>
            </div>
            <div class="w-full h-[1px] bg-[#C6C6C6] my-4"></div>
            <div class="flex flex-col">
              <div class="flex">
                <img
                  src="@/assets/images/车间@2x.png"
                  class="w-[30px] h-[30px] mr-3"
                />
                <div class="flex flex-col">
                  <span>一号车间面积 </span>
                  <span class="text-xl font-bold mt-2">256m²</span>
                </div>
              </div>
              <div class="w-full h-[1px] bg-[#C6C6C6] my-4"></div>
              <div class="flex">
                <img
                  src="@/assets/images/车间@2x.png"
                  class="w-[30px] h-[30px] mr-3"
                />
                <div class="flex flex-col">
                  <span>一号车间面积 </span>
                  <span class="text-xl font-bold mt-2">256m²</span>
                </div>
              </div>
              <div class="w-full h-[1px] bg-[#C6C6C6] my-4"></div>
              <div class="flex">
                <img
                  src="@/assets/images/车间@2x.png"
                  class="w-[30px] h-[30px] mr-3"
                />
                <div class="flex flex-col">
                  <span>一号车间面积 </span>
                  <span class="text-xl font-bold mt-2">256m²</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="14">
          <div class="box h-full">
            <div class="flex items-center">
              <img
                src="@/assets/images/标签@2x.png"
                class="w-[22px] h-[22px] mr-3"
              />
              <span class="text-xs">今年产能目标</span>
            </div>

            <div class="flex justify-between mt-6">
              <div class="flex flex-col">
                <div>
                  <span class="font-bold text-[24px]">9.5</span>
                  <span class="opacity-[0.5]">m²</span>
                </div>
                <span class="opacity-[0.5] mt-2">当年产能目标</span>
              </div>
              <div class="w-[2px] h-[full] bg-[#ffffff] opacity-[0.5]"></div>
              <div class="flex flex-col">
                <div>
                  <span class="font-bold text-[24px]">4.1</span>
                  <span class="opacity-[0.5]">m²</span>
                </div>
                <span class="opacity-[0.5] mt-2">已完成任务</span>
              </div>
              <div class="w-[2px] h-[full] bg-[#ffffff] opacity-[0.5]"></div>
              <div class="flex flex-col">
                <div>
                  <span class="font-bold text-[24px]">41</span>
                  <span class="opacity-[0.5]">%</span>
                </div>
                <span class="opacity-[0.5] mt-2">剩余任务 </span>
              </div>
            </div>

            <div class="mt-6">
              <div class="flex items-center">
                <img
                  src="@/assets/images/标签@2x.png"
                  class="w-[22px] h-[22px] mr-3"
                />
                <span class="text-xs">近六个月销售趋势</span>
              </div>
              <Chart1 ref="chart1Ref"></Chart1>
            </div>
            <div class="mt-6">
              <div class="flex items-center">
                <img
                  src="@/assets/images/标签@2x.png"
                  class="w-[22px] h-[22px] mr-3"
                />
                <span class="text-xs">产能</span>
              </div>
              <Chart2 ref="chart2Ref"></Chart2>
            </div>
            <div class="mt-6">
              <div class="flex items-center">
                <img
                  src="@/assets/images/标签@2x.png"
                  class="w-[22px] h-[22px] mr-3"
                />
                <span class="text-xs">当日生产完成率</span>
              </div>
              <Chart3 ref="chart3Ref"></Chart3>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { parseTime } from "@/utils/tools";
import { onMounted, ref, onBeforeUnmount, defineExpose } from "vue";
import Chart2 from "./chart2.vue";
import Chart1 from "./chart1.vue";
import Chart3 from "./chart3.vue";
// 图表1实例
const chart1Ref = ref<any>(null);
// 图表2实例
const chart2Ref = ref<any>(null);
// 图表3实例
const chart3Ref = ref<any>(null);

// 重新渲染
function resize() {
  chart2Ref.value?.resize();
  chart1Ref.value?.resize();
  chart3Ref.value?.resize();
}
defineExpose({ resize });
// 计时器
let timmer: number | null;
// 获取当前时间
const nowTime = ref<string | null>(parseTime(new Date(), "{h}:{i}:{s}"));
// 当天日期
const nowDate = ref<string | null>(
  parseTime(new Date(), "{y}/{m}/{d} 星期{a}")
);
// 当前星期
const nowWeek = ref<string | null>(parseTime(new Date(), "星期{a}"));
// 开始计时
function startTimmer() {
  timmer = Number(
    setInterval(() => {
      nowTime.value = parseTime(new Date(), "{h}:{i}:{s}");
      nowDate.value = parseTime(new Date(), "{y}/{m}/{d}");
      nowWeek.value = parseTime(new Date(), "星期{a}");
    }, 1000)
  );
}
// 在组件卸载前执行的代码
onBeforeUnmount(() => {
  timmer && clearInterval(timmer);
  timmer = null;
});
// 在组件挂载后执行的代码
onMounted(() => {
  startTimmer();
});
</script>

<style scoped>
.bg-r {
  background: linear-gradient(to right, #000000, #00000000);
  background: linear-gradient(90deg, #091725 0%, rgba(32, 45, 58, 0.01) 100%);
}
.bg-l {
  background: linear-gradient(90deg, #09172500 0%, #091725 100%);
}
.r-icon {
  width: 690px;
  height: 780px;
}

.btn {
  width: 125px;
  height: 40px;
  background: #161b25;
  border-radius: 6px;
  border: 1px solid;
  border-image: linear-gradient(0deg, #b4b064, #161b25) 10 10;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #b2b8c8;
  cursor: pointer;
  border-radius: 5px; /* 圆角 */
}
.btn:hover {
  color: #fffacd; /* 淡黄色字体 */
  text-shadow: 0 0 5px #b4b064, 0 0 10px rgba(255, 250, 205, 0.6),
    0 0 15px rgba(255, 250, 205, 0.4), 0 0 20px rgba(255, 250, 205, 0.2); /* 发光效果 */
  cursor: pointer; /* 鼠标悬停时变为手形指针 */
  transition: all 0.3s ease; /* 过渡效果 */
}
.btn-active {
  color: #fffacd; /* 淡黄色字体 */
  text-shadow: 0 0 5px #b4b064, 0 0 10px rgba(255, 250, 205, 0.6),
    0 0 15px rgba(255, 250, 205, 0.4), 0 0 20px rgba(255, 250, 205, 0.2); /* 发光效果 */
  padding: 10px 20px; /* 内边距 */
  transition: all 0.3s ease; /* 过渡效果 */
}

:deep(.slick-slide) {
  text-align: center;
  width: 280px !important;
  height: 165px;
  overflow: hidden;
}
.box {
  background: rgba(29, 35, 29, 0.8);
  border-radius: 10px;
  width: 100%;
  padding: 20px;
}
</style>
