# 内部模型材质应用指南

## 概述

本功能允许将 `public/modelAssets/` 目录下的 OBJ、MTL 文件和纹理图片应用到 GLB 模型上，实现更丰富的材质效果。

## 功能特性

- 自动加载 OBJ/MTL 模型文件
- 智能纹理路径修复
- 材质名称匹配算法
- 纹理自动应用到 GLB 模型
- 支持多种材质类型

## 文件结构

```
public/modelAssets/
├── AGV.obj                    # OBJ 模型文件
├── AGV.mtl                    # MTL 材质文件
├── AGV.jpg                    # 纹理图片
├── zhijia_3018_02.obj
├── zhijia_3018_02.mtl
├── tuopan_3018_03.jpg
├── 厂房_3018_01.obj
├── 厂房_3018_01.mtl
├── changfangwuding_3018_01.jpg
└── ...
```

## 使用方法

### 1. 在主页面中使用

主页面 (`src/views/home/<USER>

```typescript
// 自动加载内部模型并应用到 GLB 模型
const loadModels = async () => {
  // 首先加载内部模型资源
  const internalModels = await loadInternalModels();
  
  // 加载 GLB 模型并应用材质
  loader.load('/model/example.glb', (gltf) => {
    if (internalModels.length > 0) {
      materialManager.applyInternalMaterialsToGLB(gltf.scene, internalModels);
    }
    scene.add(gltf.scene);
  });
};
```

### 2. 使用测试页面

访问 `/material-test` 路径可以使用专门的测试页面：

1. 点击"加载内部模型"按钮加载 OBJ/MTL 模型
2. 点击"加载示例GLB"按钮加载一个 GLB 模型
3. 点击"应用材质"按钮将内部模型的材质应用到 GLB 模型上
4. 使用"重置场景"按钮清空场景

## 配置说明

### 内部模型配置

在 `MaterialManager.ts` 中的 `getInternalModelConfigs()` 方法中配置可用的内部模型：

```typescript
{
  name: 'AGV',                    // 模型名称
  objFile: 'AGV.obj',            // OBJ 文件名
  mtlFile: 'AGV.mtl',            // MTL 文件名
  textureFiles: ['AGV.jpg'],     // 纹理文件列表
  position: new THREE.Vector3(0, 0, 0),  // 可选：位置
  rotation: new THREE.Euler(0, 0, 0),    // 可选：旋转
  scale: new THREE.Vector3(1, 1, 1)      // 可选：缩放
}
```

### 材质匹配策略

系统使用以下策略匹配材质：

1. **纹理名称匹配**：根据关键词匹配（如 '3018', '3007', 'agv' 等）
2. **材质名称匹配**：直接匹配材质名称
3. **模糊匹配**：包含关系匹配
4. **默认应用**：如果没有找到匹配的纹理，使用第一个可用纹理

## 支持的文件格式

### 模型文件
- **OBJ**: Wavefront OBJ 格式
- **MTL**: Material Template Library 格式
- **GLB**: glTF Binary 格式

### 纹理文件
- **JPG/JPEG**: JPEG 图像格式
- **PNG**: PNG 图像格式
- **BMP**: Bitmap 图像格式
- **GIF**: GIF 图像格式

## 技术实现

### MaterialManager 类

核心功能类，负责：

- 加载和管理 OBJ/MTL 文件
- 纹理加载和缓存
- 材质路径修复
- 智能材质匹配和应用

### 关键方法

```typescript
// 加载内部模型
loadInternalModel(config: InternalModelConfig): Promise<THREE.Group>

// 应用材质到 GLB 模型
applyInternalMaterialsToGLB(glbModel: THREE.Object3D, internalModels: THREE.Group[])

// 加载纹理
loadTexture(path: string): Promise<THREE.Texture>

// 加载 MTL 材质
loadMTL(mtlPath: string): Promise<any>
```

## 故障排除

### 常见问题

1. **纹理路径错误**
   - 检查 MTL 文件中的纹理路径
   - 系统会自动修复常见的路径问题

2. **模型加载失败**
   - 确保 OBJ/MTL 文件格式正确
   - 检查文件路径是否存在

3. **材质不匹配**
   - 检查材质名称是否包含关键词
   - 可以在 `isTextureNameMatch` 方法中添加自定义匹配规则

### 调试信息

在浏览器控制台中查看详细的加载和应用信息：

```
Loading internal model: AGV
Applied internal materials to 15 meshes in GLB model
```

## 性能优化

1. **纹理缓存**：已加载的纹理会被缓存，避免重复加载
2. **材质复用**：相同的材质会被复用
3. **按需加载**：只加载需要的模型和纹理
4. **资源清理**：组件卸载时自动清理资源

## 扩展功能

### 添加新的内部模型

1. 将 OBJ、MTL 和纹理文件放入 `public/modelAssets/` 目录
2. 在 `getInternalModelConfigs()` 中添加配置
3. 重新加载页面即可使用

### 自定义匹配规则

在 `isTextureNameMatch` 方法中添加自定义的材质匹配逻辑：

```typescript
private isTextureNameMatch(materialName: string, textureName: string): boolean {
  // 添加自定义匹配规则
  if (materialName.includes('custom') && textureName.includes('custom')) {
    return true;
  }
  
  // 现有的匹配逻辑...
}
```

## 注意事项

1. **文件大小**：大量的纹理文件可能影响加载性能
2. **浏览器兼容性**：确保目标浏览器支持 WebGL 和相关的 Three.js 功能
3. **CORS 问题**：确保静态文件服务器正确配置 CORS 头
4. **内存管理**：及时清理不需要的资源，避免内存泄漏

## 更新日志

- **v1.0.0**: 初始版本，支持基本的 OBJ/MTL 加载和材质应用
- **v1.1.0**: 添加智能材质匹配算法
- **v1.2.0**: 支持纹理路径自动修复
- **v1.3.0**: 添加测试页面和详细的调试信息
