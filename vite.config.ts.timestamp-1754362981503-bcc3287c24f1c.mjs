// vite.config.ts
import { defineConfig } from "file:///D:/project/projectCompany/%E6%81%A9%E6%B3%B0/web/node_modules/.pnpm/vite@5.4.11_@types+node@20.17.6_terser@5.36.0/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/project/projectCompany/%E6%81%A9%E6%B3%B0/web/node_modules/.pnpm/@vitejs+plugin-vue@4.6.2_vite@5.4.11_@types+node@20.17.6_terser@5.36.0__vue@3.5.13_typescript@5.6.3_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import eslint from "file:///D:/project/projectCompany/%E6%81%A9%E6%B3%B0/web/node_modules/.pnpm/vite-plugin-eslint@1.8.1_eslint@8.57.1_vite@5.4.11_@types+node@20.17.6_terser@5.36.0_/node_modules/vite-plugin-eslint/dist/index.mjs";
import tailwindcss from "file:///D:/project/projectCompany/%E6%81%A9%E6%B3%B0/web/node_modules/.pnpm/tailwindcss@3.4.15/node_modules/tailwindcss/lib/index.js";
import autoprefixer from "file:///D:/project/projectCompany/%E6%81%A9%E6%B3%B0/web/node_modules/.pnpm/autoprefixer@10.4.20_postcss@8.4.49/node_modules/autoprefixer/lib/autoprefixer.js";
var __vite_injected_original_dirname = "D:\\project\\projectCompany\\\u6069\u6CF0\\web";
var vite_config_default = defineConfig({
  plugins: [vue(), eslint()],
  // 设置@路径
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  },
  // vite 相关配置
  server: {
    port: 8080,
    host: true,
    open: true,
    proxy: {
      "/dev-api": {
        target: "http://localhost:8880",
        changeOrigin: true,
        rewrite: (p) => p.replace(/^\/dev-api/, "")
      }
    }
  },
  build: {
    outDir: "dist",
    // 输出路径，默认为 dist
    assetsDir: "assets",
    // 静态资源文件夹名
    assetsInlineLimit: 4096,
    // 小于此大小的导入或引用资源将内联为 base64 编码，以减少 HTTP 请求
    cssCodeSplit: true,
    // 启用/禁用 CSS 代码拆分
    sourcemap: false,
    // 构建后是否生成 source map 文件
    rollupOptions: {
      // Rollup 打包配置
      // input: '/path/to/your/entry/main.js', // 自定义入口文件
      output: {
        // 输出配置
        chunkFileNames: "js/[name]-[hash].js",
        entryFileNames: "js/[name]-[hash].js",
        assetFileNames: "[ext]/[name]-[hash].[ext]"
      },
      external: [],
      // 排除打包的外部依赖
      plugins: []
      // Rollup 插件配置
    },
    terserOptions: {
      // Terser 压缩配置
      compress: {
        // 打包时去除所有console与debugger
        drop_console: true,
        drop_debugger: true
      }
    },
    manifest: true,
    // 是否生成 asset manifest 文件
    minify: "terser"
    // 指定压缩器，默认为 terser，可设为 'esbuild'
    // 更多配置...
  },
  css: {
    postcss: {
      plugins: [tailwindcss(), autoprefixer()]
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
