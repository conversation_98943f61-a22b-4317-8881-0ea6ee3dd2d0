{"name": "template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "prod": "vite --mode production", "test": "vite --mode test", "build:dev": "vue-tsc && vite build --mode development", "build:prod": " vue-tsc && vite build --mode production", "build:test": "vue-tsc && vite build --mode test", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@babel/types": "^7.23.6", "@types/three": "^0.171.0", "@types/vue": "^2.0.0", "@vue/reactivity": "^3.5.13", "@vueuse/core": "^10.7.0", "ant-design-vue": "^4.2.6", "autofit.js": "^3.2.2", "axios": "^1.6.2", "echarts": "^5.5.1", "gsap": "^3.12.5", "js-cookie": "^3.0.5", "pinia": "^2.1.7", "three": "^0.171.0", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@types/gsap": "^3.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-vue": "^4.5.2", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.7", "terser": "^5.26.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^1.8.25"}}