declare module 'three/examples/jsm/controls/OrbitControls' {
  import { Camera, EventDispatcher } from 'three';
  export class OrbitControls extends EventDispatcher {
    constructor(camera: Camera, domElement?: HTMLElement);
    enableDamping: boolean;
    update(): void;
    target: { copy: (center: any) => void };
  }
}

declare module 'three/examples/jsm/loaders/GLTFLoader' {
  import { Object3D, Scene } from 'three';
  export interface GLTF {
    scene: Scene;
    scenes: Scene[];
  }
  export class GLTFLoader {
    load(
      url: string,
      onLoad: (gltf: GLTF) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
  }
}

declare module 'three/examples/jsm/loaders/FBXLoader' {
  import { Group, Object3D } from 'three';
  export class FBXLoader {
    load(
      url: string,
      onLoad: (object: Group) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
  }
}

declare module 'three/examples/jsm/loaders/OBJLoader' {
  import { Group, Object3D } from 'three';
  export class OBJLoader {
    setMaterials(materials: any): OBJLoader;
    load(
      url: string,
      onLoad: (object: Group) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
  }
}

declare module 'three/examples/jsm/loaders/MTLLoader' {
  import { Material } from 'three';
  export interface MTLLoaderMaterialCreator {
    preload(): void;
    materials: { [key: string]: Material };
  }
  export class MTLLoader {
    setPath(path: string): MTLLoader;
    load(
      url: string,
      onLoad: (materials: MTLLoaderMaterialCreator) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
  }
}
