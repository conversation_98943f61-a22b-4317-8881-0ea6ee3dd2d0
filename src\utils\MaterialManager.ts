import * as THREE from 'three';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader';

// 材质配置接口
export interface MaterialConfig {
  objPath: string;
  mtlPath: string;
  texturePaths: string[];
  name: string;
}

// 内部模型资源配置
export interface InternalModelConfig {
  name: string;
  objFile: string;
  mtlFile: string;
  textureFiles: string[];
  position?: THREE.Vector3;
  rotation?: THREE.Euler;
  scale?: THREE.Vector3;
}

export class MaterialManager {
  private textureLoader: THREE.TextureLoader;
  private objLoader: OBJLoader;
  private mtlLoader: MTLLoader;
  private loadedMaterials: Map<string, THREE.Material[]> = new Map();
  private loadedTextures: Map<string, THREE.Texture> = new Map();

  constructor() {
    this.textureLoader = new THREE.TextureLoader();
    this.objLoader = new OBJLoader();
    this.mtlLoader = new MTLLoader();
  }

  // 获取modelAssets目录下的所有内部模型配置
  getInternalModelConfigs(): InternalModelConfig[] {
    return [
      {
        name: 'AGV',
        objFile: 'AGV.obj',
        mtlFile: 'AGV.mtl',
        textureFiles: ['AGV.jpg'],
      },
      {
        name: 'AGV1',
        objFile: 'AGV1.obj',
        mtlFile: 'AGV1.mtl',
        textureFiles: ['AGV1.jpg'],
      },
      {
        name: '支架_3018_02',
        objFile: 'zhijia_3018_02.obj',
        mtlFile: 'zhijia_3018_02.mtl',
        textureFiles: ['tuopan_3018_03.jpg'],
      },
      {
        name: '托盘_3018_01',
        objFile: 'tuopan_3018_01.obj',
        mtlFile: 'tuopan_3018_01.mtl',
        textureFiles: ['tuopan_3018_03.jpg', 'tuopan_3018_031.jpg'],
      },
      {
        name: '托盘_3018_05',
        objFile: 'tuopan_3018_05.obj',
        mtlFile: 'tuopan_3018_05.mtl',
        textureFiles: ['tuopan_3018_077.jpg'],
      },
      {
        name: '卷_01',
        objFile: 'juan_01.obj',
        mtlFile: 'juan_01.mtl',
        textureFiles: ['liao_01.jpg'],
      },
      {
        name: '厂房_3018_01',
        objFile: '厂房_3018_01.obj',
        mtlFile: '厂房_3018_01.mtl',
        textureFiles: [
          'changfangwuding_3018_01.jpg',
          'changfangxieshi_3018_01.jpg',
        ],
      },
      {
        name: '厂房_3018_02',
        objFile: '厂房_3018_02.obj',
        mtlFile: '厂房_3018_02.mtl',
        textureFiles: ['changfangwuding_3018_01.jpg'],
      },
      {
        name: '厂房_3018_03',
        objFile: '厂房_3018_03.obj',
        mtlFile: '厂房_3018_03.mtl',
        textureFiles: ['changfangwuding_3018_01.jpg'],
      },
      {
        name: '产线平台_01',
        objFile: '产线平台_01.obj',
        mtlFile: '产线平台_01.mtl',
        textureFiles: ['pingtai_3018_05.jpg'],
      },
      {
        name: '产线护栏_01',
        objFile: '产线护栏_01.obj',
        mtlFile: '产线护栏_01.mtl',
        textureFiles: ['langan.png'],
      },
      {
        name: '产线柱子_01',
        objFile: '产线柱子_01.obj',
        mtlFile: '产线柱子_01.mtl',
        textureFiles: ['zhijia_3018_02.jpg'],
      },
      {
        name: '产线设备改_04',
        objFile: '产线设备改_04.obj',
        mtlFile: '产线设备改_04.mtl',
        textureFiles: ['shebei_3007_01.jpg', 'shebei_3007_02.jpg'],
      },
      {
        name: '产线设备改_05',
        objFile: '产线设备改_05.obj',
        mtlFile: '产线设备改_05.mtl',
        textureFiles: ['shebei_3007_03.jpg', 'shebei_3007_04.jpg'],
      },
      {
        name: '产线设备改_07',
        objFile: '产线设备改_07.obj',
        mtlFile: '产线设备改_07.mtl',
        textureFiles: [
          'tongdala_3007_01.jpg',
          'tongdala_3007_02.jpg',
          'tongdala_3007_03.jpg',
        ],
      },
      {
        name: '产线设备改_08',
        objFile: '产线设备改_08.obj',
        mtlFile: '产线设备改_08.mtl',
        textureFiles: ['jixiebi_3007_01.jpg'],
      },
      {
        name: '升降机动_3018_01',
        objFile: '升降机动_3018_01.obj',
        mtlFile: '升降机动_3018_01.mtl',
        textureFiles: ['shengjiangji_3018_03.jpg'],
      },
      {
        name: '升降机改_3018_01',
        objFile: '升降机改_3018_01.obj',
        mtlFile: '升降机改_3018_01.mtl',
        textureFiles: ['tishengji_3018_01.jpg'],
      },
    ];
  }

  // 加载纹理
  async loadTexture(path: string): Promise<THREE.Texture> {
    if (this.loadedTextures.has(path)) {
      return this.loadedTextures.get(path)!;
    }

    return new Promise((resolve) => {
      this.textureLoader.load(
        `/modelAssets/${path}`,
        (texture) => {
          // 设置纹理参数
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.flipY = false; // OBJ模型通常需要设置为false
          texture.minFilter = THREE.LinearFilter;
          texture.magFilter = THREE.LinearFilter;

          this.loadedTextures.set(path, texture);
          resolve(texture);
        },
        undefined,
        (error) => {
          console.warn(`Failed to load texture: ${path}`, error);
          // 创建一个默认纹理
          const canvas = document.createElement('canvas');
          canvas.width = canvas.height = 1;
          const context = canvas.getContext('2d')!;
          context.fillStyle = '#cccccc';
          context.fillRect(0, 0, 1, 1);
          const defaultTexture = new THREE.CanvasTexture(canvas);
          this.loadedTextures.set(path, defaultTexture);
          resolve(defaultTexture);
        }
      );
    });
  }

  // 加载MTL材质文件
  async loadMTL(mtlPath: string): Promise<any> {
    return new Promise((resolve) => {
      this.mtlLoader.setPath('/modelAssets/');
      this.mtlLoader.load(
        mtlPath,
        (materials) => {
          materials.preload();
          // 修复材质中的纹理路径
          this.fixMaterialTexturePaths(materials.materials);
          resolve(materials);
        },
        undefined,
        (error) => {
          console.warn(`Failed to load MTL: ${mtlPath}`, error);
          resolve(null);
        }
      );
    });
  }

  // 修复材质中的纹理路径
  private fixMaterialTexturePaths(materials: any) {
    for (const key in materials) {
      const material = materials[key];
      if (material.map && material.map.image && material.map.image.src) {
        const originalPath = material.map.image.src;
        // 提取文件名
        const fileName = originalPath.split('/').pop();
        if (fileName) {
          // 重新设置正确的路径
          const newPath = `/modelAssets/${fileName}`;
          material.map.image.src = newPath;
        }
      }
    }
  }

  // 加载OBJ模型
  async loadOBJ(objPath: string, materials?: any): Promise<THREE.Group> {
    return new Promise((resolve, reject) => {
      if (materials) {
        this.objLoader.setMaterials(materials);
      }

      this.objLoader.load(
        `/modelAssets/${objPath}`,
        (object) => {
          resolve(object);
        },
        undefined,
        (error) => {
          console.warn(`Failed to load OBJ: ${objPath}`, error);
          reject(error);
        }
      );
    });
  }

  // 加载完整的内部模型（OBJ + MTL + 纹理）
  async loadInternalModel(
    config: InternalModelConfig
  ): Promise<THREE.Group | null> {
    try {
      console.log(`Loading internal model: ${config.name}`);

      // 1. 加载纹理
      const textures: THREE.Texture[] = [];
      for (const texturePath of config.textureFiles) {
        try {
          const texture = await this.loadTexture(texturePath);
          textures.push(texture);
        } catch (error) {
          console.warn(
            `Failed to load texture ${texturePath} for model ${config.name}`
          );
        }
      }

      // 2. 加载MTL材质
      const materials = await this.loadMTL(config.mtlFile);

      // 3. 加载OBJ模型
      const object = await this.loadOBJ(config.objFile, materials);

      // 4. 应用纹理到材质
      if (textures.length > 0 && materials) {
        this.applyTexturesToMaterials(object, textures, materials.materials);
      }

      // 5. 设置位置、旋转、缩放
      if (config.position) object.position.copy(config.position);
      if (config.rotation) object.rotation.copy(config.rotation);
      if (config.scale) object.scale.copy(config.scale);

      return object;
    } catch (error) {
      console.error(`Failed to load internal model ${config.name}:`, error);
      return null;
    }
  }

  // 将纹理应用到材质
  private applyTexturesToMaterials(
    object: THREE.Group,
    textures: THREE.Texture[],
    materials: any
  ) {
    console.log(materials);

    object.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const material = child.material as THREE.Material;

        // 如果有多个纹理，使用第一个作为主纹理
        if (textures.length > 0) {
          if (
            material instanceof THREE.MeshStandardMaterial ||
            material instanceof THREE.MeshBasicMaterial ||
            material instanceof THREE.MeshLambertMaterial ||
            material instanceof THREE.MeshPhongMaterial
          ) {
            material.map = textures[0];
            material.needsUpdate = true;
          }
        }
      }
    });
  }

  // 将内部模型的材质应用到GLB模型
  applyInternalMaterialsToGLB(
    glbModel: THREE.Object3D,
    internalModels: THREE.Group[]
  ) {
    const textureMap = new Map<string, THREE.Texture>();
    const materialMap = new Map<string, THREE.Material>();

    // 收集内部模型的所有纹理和材质
    internalModels.forEach((model) => {
      model.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          const material = child.material as any;

          // 收集纹理
          if (material.map && material.map instanceof THREE.Texture) {
            const textureName = this.getTextureFileName(material.map);
            if (textureName) {
              textureMap.set(textureName, material.map);
            }
          }

          // 收集材质
          if (material.name) {
            materialMap.set(material.name, material);
          }
        }
      });
    });

    // 应用到GLB模型
    let appliedCount = 0;
    glbModel.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const currentMaterial = child.material as any;

        // 尝试应用纹理
        if (this.applyBestMatchingTexture(currentMaterial, textureMap)) {
          appliedCount++;
        }

        // 或者尝试替换整个材质
        const matchingMaterial = this.findMatchingMaterial(
          currentMaterial,
          materialMap
        );
        if (matchingMaterial && !currentMaterial.map) {
          child.material = matchingMaterial.clone();
          child.material.needsUpdate = true;
          appliedCount++;
        }
      }
    });

    console.log(
      `Applied internal materials to ${appliedCount} meshes in GLB model`
    );
  }

  // 查找匹配的材质
  private findMatchingMaterial(
    targetMaterial: THREE.Material,
    materialMap: Map<string, THREE.Material>
  ): THREE.Material | null {
    // 首先尝试名称匹配
    if (targetMaterial.name && materialMap.has(targetMaterial.name)) {
      return materialMap.get(targetMaterial.name)!;
    }

    // 如果没有直接匹配，尝试模糊匹配
    for (const [name, material] of materialMap) {
      console.log(name);

      if (this.isMaterialSimilar(targetMaterial, material)) {
        return material;
      }
    }

    return null;
  }

  // 判断材质是否相似
  private isMaterialSimilar(
    material1: THREE.Material,
    material2: THREE.Material
  ): boolean {
    // 简单的相似性检查，可以根据需要扩展
    return material1.type === material2.type;
  }

  // 获取纹理文件名
  private getTextureFileName(texture: THREE.Texture): string | null {
    if (texture.image && texture.image.src) {
      const url = texture.image.src;
      const fileName = url.split('/').pop();
      return fileName || null;
    }
    return null;
  }

  // 应用最佳匹配的纹理
  private applyBestMatchingTexture(
    material: any,
    textureMap: Map<string, THREE.Texture>
  ): boolean {
    // 如果材质已经有纹理，跳过
    if (material.map) {
      return false;
    }

    // 尝试根据材质名称匹配纹理
    if (material.name) {
      for (const [textureName, texture] of textureMap) {
        if (this.isTextureNameMatch(material.name, textureName)) {
          material.map = texture;
          material.needsUpdate = true;
          return true;
        }
      }
    }

    // 如果没有找到匹配的纹理，使用第一个可用的纹理
    if (textureMap.size > 0) {
      const firstTexture = textureMap.values().next().value;
      material.map = firstTexture;
      material.needsUpdate = true;
      return true;
    }

    return false;
  }

  // 检查纹理名称是否匹配
  private isTextureNameMatch(
    materialName: string,
    textureName: string
  ): boolean {
    const materialLower = materialName.toLowerCase();
    const textureLower = textureName.toLowerCase();

    // 移除文件扩展名
    const textureBaseName = textureLower.replace(
      /\.(jpg|jpeg|png|bmp|gif)$/,
      ''
    );

    // 检查是否包含相似的关键词
    const keywords = [
      '3018',
      '3007',
      '3017',
      'agv',
      'tuopan',
      'zhijia',
      'shebei',
    ];

    for (const keyword of keywords) {
      if (
        materialLower.includes(keyword) &&
        textureBaseName.includes(keyword)
      ) {
        return true;
      }
    }

    // 检查直接包含关系
    return (
      materialLower.includes(textureBaseName) ||
      textureBaseName.includes(materialLower)
    );
  }

  // 清理资源
  dispose() {
    this.loadedTextures.forEach((texture) => texture.dispose());
    this.loadedTextures.clear();
    this.loadedMaterials.clear();
  }
}
