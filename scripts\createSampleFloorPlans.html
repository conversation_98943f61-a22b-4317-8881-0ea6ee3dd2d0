<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成示例平面图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .floorplan {
            width: 400px;
            height: 300px;
            border: 2px solid #333;
            margin: 20px 0;
            position: relative;
            background: #f9f9f9;
        }
        .room {
            position: absolute;
            border: 1px solid #666;
            background: rgba(100, 150, 200, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3D模型平面图生成器</h1>
        <p>这个工具可以帮助您生成示例平面图用于测试3D模型内部贴图功能。</p>
        
        <!-- 数字化装备大楼平面图 -->
        <h3>数字化装备大楼 - 一楼平面图</h3>
        <div class="floorplan" id="building1-floor1">
            <div class="room" style="left: 20px; top: 20px; width: 80px; height: 60px;">大厅</div>
            <div class="room" style="left: 120px; top: 20px; width: 100px; height: 60px;">接待室</div>
            <div class="room" style="left: 240px; top: 20px; width: 120px; height: 60px;">会议室</div>
            <div class="room" style="left: 20px; top: 100px; width: 150px; height: 80px;">展示区</div>
            <div class="room" style="left: 190px; top: 100px; width: 170px; height: 80px;">办公区</div>
            <div class="room" style="left: 20px; top: 200px; width: 340px; height: 60px;">设备间</div>
        </div>
        <button class="download-btn" onclick="downloadFloorPlan('building1-floor1', 'digital-building-floor1.jpg')">下载平面图</button>

        <!-- 行政办公楼平面图 -->
        <h3>行政办公楼 - 平面图</h3>
        <div class="floorplan" id="admin-building">
            <div class="room" style="left: 30px; top: 30px; width: 100px; height: 80px;">总经理室</div>
            <div class="room" style="left: 150px; top: 30px; width: 80px; height: 80px;">副总室</div>
            <div class="room" style="left: 250px; top: 30px; width: 100px; height: 80px;">财务室</div>
            <div class="room" style="left: 30px; top: 130px; width: 120px; height: 100px;">人事部</div>
            <div class="room" style="left: 170px; top: 130px; width: 180px; height: 100px;">开放办公区</div>
            <div class="room" style="left: 30px; top: 250px; width: 320px; height: 30px;">走廊</div>
        </div>
        <button class="download-btn" onclick="downloadFloorPlan('admin-building', 'admin-building-floor1.jpg')">下载平面图</button>

        <!-- 食堂平面图 -->
        <h3>食堂 - 平面图</h3>
        <div class="floorplan" id="canteen">
            <div class="room" style="left: 20px; top: 20px; width: 120px; height: 100px;">厨房</div>
            <div class="room" style="left: 160px; top: 20px; width: 200px; height: 100px;">用餐区A</div>
            <div class="room" style="left: 20px; top: 140px; width: 160px; height: 120px;">用餐区B</div>
            <div class="room" style="left: 200px; top: 140px; width: 160px; height: 120px;">用餐区C</div>
        </div>
        <button class="download-btn" onclick="downloadFloorPlan('canteen', 'canteen-layout.jpg')">下载平面图</button>

        <h2>使用说明</h2>
        <ol>
            <li>点击"下载平面图"按钮保存图片到本地</li>
            <li>将图片放置到项目的 <code>public/img/floorplans/</code> 目录下</li>
            <li>在 <code>src/config/floorPlanConfig.ts</code> 中配置对应的模型和纹理路径</li>
            <li>重新加载页面查看效果</li>
        </ol>

        <h2>高级功能</h2>
        <ul>
            <li>按 'D' 键显示/隐藏调试面板</li>
            <li>按 'W' 键切换线框模式</li>
            <li>按 'B' 键显示/隐藏边界盒</li>
            <li>按 'R' 键重置相机位置</li>
        </ul>
    </div>

    <script>
        function downloadFloorPlan(elementId, filename) {
            const element = document.getElementById(elementId);
            
            // 创建canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 400;
            canvas.height = 300;
            
            // 绘制背景
            ctx.fillStyle = '#f9f9f9';
            ctx.fillRect(0, 0, 400, 300);
            
            // 绘制边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, 400, 300);
            
            // 绘制房间
            const rooms = element.querySelectorAll('.room');
            rooms.forEach(room => {
                const style = room.style;
                const left = parseInt(style.left);
                const top = parseInt(style.top);
                const width = parseInt(style.width);
                const height = parseInt(style.height);
                
                // 绘制房间背景
                ctx.fillStyle = 'rgba(100, 150, 200, 0.3)';
                ctx.fillRect(left, top, width, height);
                
                // 绘制房间边框
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                ctx.strokeRect(left, top, width, height);
                
                // 绘制房间名称
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(room.textContent, left + width/2, top + height/2 + 4);
            });
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            });
        }
    </script>
</body>
</html>
