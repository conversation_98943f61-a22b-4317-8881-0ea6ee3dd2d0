<template>
  <div class="relative text-[#FDFEFF] px-7">
    <div ref="chartRef" class="h-[200px]"></div>
  </div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted, onUnmounted, defineExpose, ref } from 'vue';

// 容器实例
const chartRef = ref(null);
// 图表实例
let myChart: any = null;

// 重新渲染
function resize() {
  myChart && myChart.resize();
}
defineExpose({ resize });
// 初始化图表
function initChart() {
  myChart = echarts.init(chartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    color: ['#79FBCF', '#EDD555'],
    legend: {
      show: false,
      right: '2%',
      textStyle: {
        color: '#FDFEFF',
      },
      itemStyle: {
        borderWidth: '10px',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',

      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        axisLabel: {
          color: '#FDFEFF',
        },
        axisLine: {
          lineStyle: {
            color: '#A4DAFF',
            opacity: 0.2,
          },
        },
        axisTick: {
          //坐标轴刻度
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#FDFEFF',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#A4DAFF',
            type: 'dashed',
            opacity: 0.2,
          },
        },
      },
    ],
    series: [
      {
        smooth: true, // 是否平滑曲线显示
        name: '总用电量',
        symbol: 'circle', //变为实心圆
        type: 'line',
        symbolSize: 8, // 折点大小
        //渐变颜色
        areaStyle: {
          color: {
            type: 'linear', // 线性渐变
            x: 0, // 起始位置
            y: 0,
            x2: 0, // 结束位置
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(121, 251, 207, .5)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(121, 251, 207, 0)', // 0% 处的颜色
              },
            ],
          },
        },
        data: [2220, 2430, 2310, 242, 290, 2700, 200],
      },
      {
        smooth: true, // 是否平滑曲线显示
        symbol: 'circle', //变为实心圆
        name: '光伏发电',
        type: 'line',
        symbolSize: 8, // 折点大小
        //渐变颜色
        areaStyle: {
          color: {
            type: 'linear', // 线性渐变
            x: 0, // 起始位置
            y: 0,
            x2: 0, // 结束位置
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(237, 213, 85, .5)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(237, 213, 85, 0)', // 0% 处的颜色
              },
            ],
          },
        },
        data: [1220, 210, 1110, 254, 2600, 280, 270],
      },
    ],
  };
  option && myChart.setOption(option);
}
onMounted(() => {
  initChart();
});
onUnmounted(() => {
  myChart && myChart.dispose();
});
</script>
