<template>
  <div class="material-test-container">
    <!-- 3D场景容器 -->
    <div ref="threeContainer" class="three-container"></div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>材质应用测试</h3>
      <div class="controls">
        <button @click="loadInternalModels" :disabled="loading">
          加载内部模型
        </button>
        <button @click="loadSampleGLB" :disabled="loading">
          加载示例GLB
        </button>
        <button @click="applyMaterials" :disabled="!hasInternalModels || !hasGLBModel">
          应用材质
        </button>
        <button @click="resetScene">
          重置场景
        </button>
      </div>
      
      <div class="status">
        <p>状态: {{ status }}</p>
        <p>内部模型: {{ internalModelCount }} 个</p>
        <p>GLB模型: {{ hasGLBModel ? '已加载' : '未加载' }}</p>
      </div>
      
      <div class="model-list">
        <h4>可用的内部模型:</h4>
        <ul>
          <li v-for="config in availableModels" :key="config.name">
            {{ config.name }} ({{ config.textureFiles.length }} 个纹理)
          </li>
        </ul>
      </div>
    </div>

    <!-- 加载进度 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-text">{{ loadingText }}</div>
        <div class="loading-progress">
          <div
            class="progress-bar"
            :style="{ width: loadingProgress + '%' }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { MaterialManager, InternalModelConfig } from '@/utils/MaterialManager';

// 响应式数据
const threeContainer = ref<HTMLDivElement>();
const loading = ref(false);
const loadingProgress = ref(0);
const loadingText = ref('');
const status = ref('准备就绪');
const internalModelCount = ref(0);
const hasGLBModel = ref(false);
const hasInternalModels = ref(false);

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let animationId: number;
let materialManager: MaterialManager;
let loadedInternalModels: THREE.Group[] = [];
let loadedGLBModel: THREE.Group | null = null;

// 可用的模型配置
const availableModels = ref<InternalModelConfig[]>([]);

// 初始化Three.js场景
const initThreeJS = () => {
  if (!threeContainer.value) return;

  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x87ceeb);

  // 创建相机
  const width = threeContainer.value.clientWidth;
  const height = threeContainer.value.clientHeight;
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
  camera.position.set(50, 50, 50);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  threeContainer.value.appendChild(renderer.domElement);

  // 创建控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;

  // 创建材质管理器
  materialManager = new MaterialManager();
  availableModels.value = materialManager.getInternalModelConfigs();

  // 添加光源
  addLights();

  // 开始渲染循环
  animate();
};

// 添加光源
const addLights = () => {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
  scene.add(ambientLight);

  // 方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(50, 50, 25);
  directionalLight.castShadow = true;
  scene.add(directionalLight);

  // 添加坐标轴辅助器
  const axesHelper = new THREE.AxesHelper(20);
  scene.add(axesHelper);

  // 添加网格辅助器
  const gridHelper = new THREE.GridHelper(100, 10);
  scene.add(gridHelper);
};

// 加载内部模型
const loadInternalModels = async () => {
  loading.value = true;
  loadingText.value = '正在加载内部模型...';
  status.value = '加载中...';
  
  try {
    const configs = materialManager.getInternalModelConfigs().slice(0, 5); // 只加载前5个模型进行测试
    loadedInternalModels = [];
    
    for (let i = 0; i < configs.length; i++) {
      const config = configs[i];
      loadingText.value = `正在加载: ${config.name}`;
      loadingProgress.value = (i / configs.length) * 100;
      
      try {
        const model = await materialManager.loadInternalModel(config);
        if (model) {
          // 设置模型位置
          model.position.set(i * 15 - 30, 0, -30);
          model.scale.setScalar(0.5);
          scene.add(model);
          loadedInternalModels.push(model);
        }
      } catch (error) {
        console.warn(`Failed to load ${config.name}:`, error);
      }
    }
    
    internalModelCount.value = loadedInternalModels.length;
    hasInternalModels.value = loadedInternalModels.length > 0;
    status.value = `已加载 ${loadedInternalModels.length} 个内部模型`;
  } catch (error) {
    console.error('Failed to load internal models:', error);
    status.value = '加载失败';
  } finally {
    loading.value = false;
    loadingProgress.value = 100;
  }
};

// 加载示例GLB模型
const loadSampleGLB = async () => {
  loading.value = true;
  loadingText.value = '正在加载GLB模型...';
  status.value = '加载GLB中...';
  
  try {
    const loader = new GLTFLoader();
    const modelFiles = [
      '01shuzihuazhuangbeidalou-1030.glb',
      '03xingzhengbangonglou-1030.glb',
      '06shitang-1030.glb'
    ];
    
    // 随机选择一个模型
    const randomModel = modelFiles[Math.floor(Math.random() * modelFiles.length)];
    
    await new Promise<void>((resolve, reject) => {
      loader.load(
        `/model/${randomModel}`,
        (gltf) => {
          if (loadedGLBModel) {
            scene.remove(loadedGLBModel);
          }
          
          loadedGLBModel = gltf.scene;
          loadedGLBModel.position.set(0, 0, 0);
          loadedGLBModel.scale.setScalar(0.5);
          
          // 启用阴影
          loadedGLBModel.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
          
          scene.add(loadedGLBModel);
          hasGLBModel.value = true;
          status.value = `已加载GLB模型: ${randomModel}`;
          resolve();
        },
        (progress) => {
          loadingProgress.value = (progress.loaded / progress.total) * 100;
        },
        (error) => {
          console.error('Failed to load GLB:', error);
          status.value = 'GLB加载失败';
          reject(error);
        }
      );
    });
  } catch (error) {
    console.error('Failed to load GLB model:', error);
    status.value = 'GLB加载失败';
  } finally {
    loading.value = false;
  }
};

// 应用材质
const applyMaterials = () => {
  if (!loadedGLBModel || loadedInternalModels.length === 0) {
    status.value = '请先加载模型';
    return;
  }
  
  status.value = '正在应用材质...';
  
  try {
    materialManager.applyInternalMaterialsToGLB(loadedGLBModel, loadedInternalModels);
    status.value = '材质应用完成';
  } catch (error) {
    console.error('Failed to apply materials:', error);
    status.value = '材质应用失败';
  }
};

// 重置场景
const resetScene = () => {
  // 清除所有模型
  if (loadedGLBModel) {
    scene.remove(loadedGLBModel);
    loadedGLBModel = null;
  }
  
  loadedInternalModels.forEach(model => {
    scene.remove(model);
  });
  loadedInternalModels = [];
  
  hasGLBModel.value = false;
  hasInternalModels.value = false;
  internalModelCount.value = 0;
  status.value = '场景已重置';
};

// 渲染循环
const animate = () => {
  animationId = requestAnimationFrame(animate);
  controls.update();
  renderer.render(scene, camera);
};

// 处理窗口大小变化
const handleResize = () => {
  if (!threeContainer.value || !camera || !renderer) return;

  const width = threeContainer.value.clientWidth;
  const height = threeContainer.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
};

// 组件挂载
onMounted(() => {
  initThreeJS();
  window.addEventListener('resize', handleResize);
});

// 组件卸载
onBeforeUnmount(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  window.removeEventListener('resize', handleResize);

  if (renderer) {
    renderer.dispose();
  }

  if (materialManager) {
    materialManager.dispose();
  }
});
</script>

<style scoped>
.material-test-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  z-index: 2;
  max-height: 80vh;
  overflow-y: auto;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.controls button {
  padding: 10px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.controls button:hover:not(:disabled) {
  background: #0056b3;
}

.controls button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.status {
  margin-bottom: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.status p {
  margin: 5px 0;
  font-size: 14px;
}

.model-list {
  max-height: 200px;
  overflow-y: auto;
}

.model-list h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.model-list ul {
  margin: 0;
  padding-left: 20px;
}

.model-list li {
  font-size: 12px;
  margin: 5px 0;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  font-size: 18px;
  margin-bottom: 20px;
}

.loading-progress {
  width: 300px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  transition: width 0.3s ease;
}
</style>
