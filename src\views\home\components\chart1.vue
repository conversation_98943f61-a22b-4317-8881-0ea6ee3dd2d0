<template>
  <div class="p-2">
    <div class="relative text-[#FDFEFF]">
      <div ref="chartRef" class="h-[150px]"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted, onUnmounted, defineExpose, ref } from 'vue';

// 容器实例
const chartRef = ref(null);
// 图表实例
let myChart: any = null;

// 重新渲染
function resize() {
  myChart && myChart.resize();
}
defineExpose({ resize });
// 初始化图表
function initChart() {
  myChart = echarts.init(chartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    color: ['#EEE353'],
    legend: {
      show: false,
      textStyle: {
        color: '#FDFEFF',
      },
      itemStyle: {
        borderWidth: '10px',
      },
    },
    grid: {
      left: '2%',
      right: '0%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['2月', '3月', '4月', '5月', '6月', '7月'],
        axisLabel: {
          lineHeight: 30,
          color: '#FDFEFF',
        },
        axisLine: {
          lineStyle: {
            color: '#A4DAFF',
            opacity: 0.2,
          },
        },
        axisTick: {
          //坐标轴刻度
          show: false,
        },
      },
    ],
    yAxis: [
      {
        interval: 200, // 步长
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#FDFEFF',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#A4DAFF',
            type: 'dashed',
            opacity: 0.2,
          },
        },
      },
    ],
    series: [
      {
        smooth: true, // 是否平滑曲线显示
        name: '总用电量',
        symbol: 'circle', //变为实心圆
        type: 'line',
        symbolSize: 6, // 折点大小
        itemStyle: {
          normal: {
            color: '#BECCCC',
            borderWidth: 2, // symbol边框
            borderColor: '#EEE353', // symbol边框颜色
          },
        },
        lineStyle: {
          color: '#EEE353',
        },
        //渐变颜色
        areaStyle: {
          color: {
            type: 'linear', // 线性渐变
            x: 0, // 起始位置
            y: 0,
            x2: 0, // 结束位置
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(71, 226, 220,0)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(18, 165, 245,0)', // 0% 处的颜色
              },
            ],
          },
        },
        data: [120, 23, 254, 142, 190, 330],
      },
    ],
  };
  option && myChart.setOption(option);
}
onMounted(() => {
  initChart();
});
onUnmounted(() => {
  myChart && myChart.dispose();
});
</script>
