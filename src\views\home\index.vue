<template>
  <div class="home-container">
    <!-- 3D场景容器 -->
    <div ref="threeContainer" class="three-container"></div>

    <!-- UI覆盖层 -->
    <div class="ui-overlay">
      <Container ref="containerRef" />

      <!-- 材质测试按钮 -->
      <div class="material-test-panel">
        <button @click="goToMaterialTest" class="test-button">
          材质应用测试
        </button>
        <p class="test-info">测试内部模型材质应用功能</p>
      </div>
    </div>

    <!-- 加载进度 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-text">正在加载3D模型...</div>
        <div class="loading-progress">
          <div
            class="progress-bar"
            :style="{ width: loadingProgress + '%' }"
          ></div>
        </div>
        <div class="loading-info">{{ loadingInfo }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import Container from './components/container.vue';
import { MaterialManager } from '@/utils/MaterialManager';

// 路由
const router = useRouter();

// 响应式数据
const threeContainer = ref<HTMLDivElement>();
const containerRef = ref();
const loading = ref(true);
const loadingProgress = ref(0);
const loadingInfo = ref('');

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let animationId: number;
let materialManager: MaterialManager;

// 模型文件列表
const modelFiles = [
  '01shuzihuazhuangbeidalou-1030.glb',
  '01xinnengyuanzhuangbeichejian-1030.glb',
  '02bkeyanjidi-1204.glb',
  '02lilanchejian-1204.glb',
  '03xingzhengbangonglou-1030.glb',
  '04daobansushe-1030.glb',
  '05daxueshenggongyuA-1121.glb',
  '05daxueshenggongyuB-1121.glb',
  '05daxueshenggongyuC-1121.glb',
  '06shitang-1030.glb',
  '07donglizhongxin-1030.glb',
  '08bangongshi-1030.glb',
  '09wuliuchangchubu-1030.glb',
  '10dqxiaofangshuichi-1030.glb',
  '11jigaiwuzibuxianku-1030.glb',
  '12youku-1030.glb',
  '13wuliubuchangpeng-1206.glb',
  '14fanghuoxianlanchejian-1030.glb',
  '15daoxianchejian-1030.glb',
  '16telanchejian-1030.glb',
  '17daoxianchejianBBB-1030.glb',
  '18telanchejianxiangtaojian-1030.glb',
  '19dimianjianzhu-250106.glb',
  '20dimiandianlanA-1026-d1.glb',
  '20dimiandianlanD-1026-d1.glb',
  '21weixinxiaofnangzhan-1030.glb',
  '22buxiandianlanchejian-1030.glb',
  '23xintegongsi-1030.glb',
  '24fuzhaochejian-1030.glb',
  '25zhijianzhongxin-1030.glb',
  '26shebeibuweixiugongduan-1030.glb',
  '27zhawuchang-1030.glb',
  '28xiqufafang-1030.glb',
  '29dlzhiwu-25010603.glb',
  '30DLdimian-1101-1.glb',
  '31DLxiaofangsuan-120516.glb',
  '32guolufang-1206.glb',
  '33youku-1206.glb',
  '34zhawujian-1206.glb',
  '35changfang01-250106.glb',
  '36changfang02-25010618.glb',
];

// 初始化Three.js场景
const initThreeJS = () => {
  if (!threeContainer.value) return;

  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x87ceeb); // 天蓝色背景

  // 创建相机
  const width = threeContainer.value.clientWidth;
  const height = threeContainer.value.clientHeight;
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 10000);
  camera.position.set(100, 100, 100);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  threeContainer.value.appendChild(renderer.domElement);

  // 创建控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;

  // 创建材质管理器
  materialManager = new MaterialManager();

  // 添加光源
  addLights();

  // 开始渲染循环
  animate();
};

// 添加光源
const addLights = () => {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
  scene.add(ambientLight);

  // 方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(100, 100, 50);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  scene.add(directionalLight);

  // 点光源
  const pointLight = new THREE.PointLight(0xffffff, 0.5, 1000);
  pointLight.position.set(0, 50, 0);
  scene.add(pointLight);
};

// 加载内部模型
const loadInternalModels = async (): Promise<THREE.Group[]> => {
  const configs = materialManager.getInternalModelConfigs();
  const loadedModels: THREE.Group[] = [];

  for (const config of configs) {
    try {
      const model = await materialManager.loadInternalModel(config);
      if (model) {
        loadedModels.push(model);
      }
    } catch (error) {
      console.warn(`Failed to load internal model ${config.name}:`, error);
    }
  }

  return loadedModels;
};

// 将内部模型添加到场景中
const addInternalModelsToScene = (internalModels: THREE.Group[]) => {
  internalModels.forEach((model, index) => {
    // 将内部模型放置在场景的一个角落，作为参考
    model.position.set(-200 + index * 20, 0, -200);
    model.scale.setScalar(0.5); // 缩小显示
    scene.add(model);
  });
};

// 加载所有模型
const loadModels = async () => {
  const loader = new GLTFLoader();
  const totalModels = modelFiles.length;
  let loadedModels = 0;

  // 首先加载内部模型资源
  loadingInfo.value = '正在加载内部模型资源...';
  const internalModels = await loadInternalModels();

  // 计算模型排列的网格大小
  const gridSize = Math.ceil(Math.sqrt(totalModels));
  const spacing = 50; // 模型之间的间距

  for (let i = 0; i < modelFiles.length; i++) {
    const modelFile = modelFiles[i];
    loadingInfo.value = `正在加载: ${modelFile}`;

    try {
      await new Promise<void>((resolve) => {
        loader.load(
          `/model/${modelFile}`,
          (gltf) => {
            // 计算模型在网格中的位置
            const row = Math.floor(i / gridSize);
            const col = i % gridSize;
            const x = (col - gridSize / 2) * spacing;
            const z = (row - gridSize / 2) * spacing;

            // 设置模型位置
            gltf.scene.position.set(x, 0, z);

            // 应用内部模型的材质到GLB模型
            if (internalModels.length > 0) {
              materialManager.applyInternalMaterialsToGLB(
                gltf.scene,
                internalModels
              );
            }

            // 启用阴影
            gltf.scene.traverse((child) => {
              if (child instanceof THREE.Mesh) {
                child.castShadow = true;
                child.receiveShadow = true;
              }
            });

            // 添加到场景
            scene.add(gltf.scene);

            loadedModels++;
            loadingProgress.value = (loadedModels / totalModels) * 100;
            resolve();
          },
          (progress) => {
            // 加载进度
            console.log(
              `Loading ${modelFile}: ${
                (progress.loaded / progress.total) * 100
              }%`
            );
          },
          (error) => {
            console.error(`Error loading ${modelFile}:`, error);
            loadedModels++;
            loadingProgress.value = (loadedModels / totalModels) * 100;
            resolve(); // 即使加载失败也继续
          }
        );
      });
    } catch (error) {
      console.error(`Failed to load ${modelFile}:`, error);
    }
  }

  // 将内部模型也添加到场景中（可选）
  addInternalModelsToScene(internalModels);

  loading.value = false;
  loadingInfo.value = '加载完成';
};

// 渲染循环
const animate = () => {
  animationId = requestAnimationFrame(animate);
  controls.update();
  renderer.render(scene, camera);
};

// 处理窗口大小变化
const handleResize = () => {
  if (!threeContainer.value || !camera || !renderer) return;

  const width = threeContainer.value.clientWidth;
  const height = threeContainer.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);

  // 调用容器组件的resize方法
  containerRef.value?.resize();
};

// 跳转到材质测试页面
const goToMaterialTest = () => {
  router.push('/material-test');
};

// 组件挂载
onMounted(() => {
  initThreeJS();
  loadModels();
  window.addEventListener('resize', handleResize);
});

// 组件卸载
onBeforeUnmount(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  window.removeEventListener('resize', handleResize);

  // 清理Three.js资源
  if (renderer) {
    renderer.dispose();
  }

  // 清理材质管理器资源
  if (materialManager) {
    materialManager.dispose();
  }
});
</script>

<style scoped>
.home-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.ui-overlay > * {
  pointer-events: auto;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  font-size: 24px;
  margin-bottom: 20px;
}

.loading-progress {
  width: 300px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  transition: width 0.3s ease;
}

.loading-info {
  font-size: 14px;
  opacity: 0.8;
}

.material-test-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  z-index: 10;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.test-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.test-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.test-button:active {
  transform: translateY(0);
}

.test-info {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style>
