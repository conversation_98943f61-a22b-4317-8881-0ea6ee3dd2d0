// 日期格式化
export function parseTime(time: Date | string | number, pattern?: string) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date: Date;

  if (typeof time === "object" && time instanceof Date) {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time, 10);
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "/")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "");
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }

    date = new Date(time);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date provided");
    }
  }
  const formatObj: {
    y: number;
    m: number;
    d: number;
    h: number;
    i: number;
    s: number;
    a: number;
  } = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(
    /{(y|m|d|h|i|s|a)+}/g,
    (result, key: "y" | "m" | "d" | "h" | "i" | "s" | "a") => {
      let value: number | string = formatObj[key];
      if (key === "a") {
        return ["日", "一", "二", "三", "四", "五", "六"][value];
      }
      // Add leading zero if necessary
      if (result.length > 0 && value < 10) {
        value = "0" + value;
      }
      return value.toString();
    }
  );

  return time_str;
}
