// 3D模型内部平面图配置文件
export interface FloorPlanConfig {
  modelName: string; // 模型文件名（不含扩展名）
  texturePath: string; // 纹理图片路径
  materialName?: string; // 特定材质名称（可选）
  uvScale?: { x: number; y: number }; // UV缩放
  uvOffset?: { x: number; y: number }; // UV偏移
  position?: { x: number; y: number; z: number }; // 自定义位置
  rotation?: { x: number; y: number; z: number }; // 自定义旋转
  size?: { width: number; height: number }; // 自定义尺寸
  opacity?: number; // 透明度
  floors?: FloorConfig[]; // 多楼层配置
}

export interface FloorConfig {
  level: number; // 楼层
  texturePath: string; // 该楼层的平面图
  yOffset: number; // Y轴偏移（高度）
}

// 默认配置
export const defaultFloorPlanConfigs: FloorPlanConfig[] = [
  {
    modelName: '01shuzihuazhuangbeidalou-1030',
    texturePath: '/img/floorplans/digital-building-floor1.jpg',
    uvScale: { x: 1, y: 1 },
    uvOffset: { x: 0, y: 0 },
    opacity: 0.9,
    floors: [
      {
        level: 1,
        texturePath: '/img/floorplans/digital-building-floor1.jpg',
        yOffset: 0.1
      },
      {
        level: 2,
        texturePath: '/img/floorplans/digital-building-floor2.jpg',
        yOffset: 10
      },
      {
        level: 3,
        texturePath: '/img/floorplans/digital-building-floor3.jpg',
        yOffset: 20
      }
    ]
  },
  {
    modelName: '03xingzhengbangonglou-1030',
    texturePath: '/img/floorplans/admin-building-floor1.jpg',
    materialName: 'FloorMaterial', // 指定要替换的材质
    uvScale: { x: 1.2, y: 1.2 },
    uvOffset: { x: 0, y: 0 },
    opacity: 0.8
  },
  {
    modelName: '06shitang-1030',
    texturePath: '/img/floorplans/canteen-layout.jpg',
    uvScale: { x: 1, y: 1 },
    uvOffset: { x: 0, y: 0 },
    position: { x: 0, y: 0.5, z: 0 }, // 自定义位置
    size: { width: 30, height: 20 } // 自定义尺寸
  },
  {
    modelName: '05daxueshenggongyuA-1121',
    texturePath: '/img/floorplans/dormitory-a-layout.jpg',
    uvScale: { x: 1, y: 1 },
    uvOffset: { x: 0, y: 0 },
    floors: [
      {
        level: 1,
        texturePath: '/img/floorplans/dormitory-a-floor1.jpg',
        yOffset: 0.1
      },
      {
        level: 2,
        texturePath: '/img/floorplans/dormitory-a-floor2.jpg',
        yOffset: 8
      },
      {
        level: 3,
        texturePath: '/img/floorplans/dormitory-a-floor3.jpg',
        yOffset: 16
      }
    ]
  },
  {
    modelName: '07donglizhongxin-1030',
    texturePath: '/img/floorplans/power-center-layout.jpg',
    materialName: 'PowerFloor',
    uvScale: { x: 2, y: 2 }, // 纹理重复
    uvOffset: { x: 0, y: 0 }
  },
  {
    modelName: '08bangongshi-1030',
    texturePath: '/img/floorplans/office-layout.jpg',
    uvScale: { x: 1, y: 1 },
    uvOffset: { x: 0.1, y: 0.1 }, // 稍微偏移
    opacity: 0.7
  },
  {
    modelName: '25zhijianzhongxin-1030',
    texturePath: '/img/floorplans/quality-center-layout.jpg',
    uvScale: { x: 1.5, y: 1.5 },
    uvOffset: { x: 0, y: 0 },
    rotation: { x: -Math.PI / 2, y: 0, z: Math.PI / 4 } // 45度旋转
  }
];

// 工具函数：根据模型名称获取配置
export const getFloorPlanConfig = (modelName: string): FloorPlanConfig | undefined => {
  return defaultFloorPlanConfigs.find(config => config.modelName === modelName);
};

// 工具函数：获取所有配置的模型名称
export const getConfiguredModelNames = (): string[] => {
  return defaultFloorPlanConfigs.map(config => config.modelName);
};

// 工具函数：检查模型是否有多楼层配置
export const hasMultipleFloors = (modelName: string): boolean => {
  const config = getFloorPlanConfig(modelName);
  return !!(config?.floors && config.floors.length > 1);
};
