<template>
  <div class="relative text-[#FDFEFF] flex justify-between">
    <div class="flex flex-col mt-10">
      <div class="flex items-center">
        <span class="ml-0">产能</span>
        <span class="ml-10 text-[#EDD555]">
          <span class="font-bold text-[24px]">506.6</span>
          <span class="font-bold">kws</span>
        </span>
      </div>
      <div class="flex items-center mt-10">
        <span class="ml-2">完成率</span>
        <span class="ml-10 text-[#EDD555]">
          <span class="font-bold text-[24px]">98%</span>
        </span>
      </div>
    </div>
    <div
      ref="chartRef"
      class="h-[150px] w-[150px] flex items-center justify-center"
    ></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted, ref } from 'vue';

const chartRef = ref(null);

const initChart = () => {
  const chart = echarts.init(chartRef.value);

  let cx = 150 / 2;
  let cy = 150 / 2;

  let option = {
    graphic: [
      // 圆心
      {
        type: 'circle',
        shape: {
          cx: cx,
          cy: cy,
          r: 25,
        },
        style: {
          fill: '#203772',
        },
      },
    ],
    series: [
      // 仪表盘
      {
        type: 'gauge',
        startAngle: 235, // 起始角度
        endAngle: -55, // 结束角度
        radius: '70%', // 设置仪表盘的半径
        center: ['50%', '52%'],

        // 仪表盘轴线相关配置
        axisLine: {
          lineStyle: {
            width: 20,
          },
          roundCap: false,
        },
        // 仪表盘刻度样式
        axisTick: {
          show: false,
        },
        // 仪表盘的分割线样式
        splitLine: {
          show: false,
        },
        // 仪表盘的指针样式
        pointer: {
          showAbove: false,
          width: 0,
        },
        axisLabel: {
          show: false,
        },
        splitNumber: 0, // 仪表盘刻度的分割段数
        progress: {
          show: true,
          width: 20,
          roundCap: false,
          itemStyle: {
            // 渐变颜色
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#3559B1' },
              { offset: 0.5, color: '#3559B1' },
              { offset: 1, color: '#1B2D61' },
            ]),
          },
        },
        detail: {
          valueAnimation: true,
          fontSize: 16,
          fontWeight: 'bold',
          color: '#FFFFFF',
          offsetCenter: [0, '0%'],
          formatter: '{value}%',
        },
        data: [
          {
            value: 96,
          },
        ],
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener('resize', () => {
    chart.resize();
  });
};

onMounted(() => {
  initChart();
});
</script>
