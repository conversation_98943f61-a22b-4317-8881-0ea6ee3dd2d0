# Three.js 模型内部3D平面图贴图指南

## 概述
本指南介绍如何在Three.js中为3D模型内部添加平面图纹理，实现建筑物内部布局的可视化。

## 实现方法

### 方法1：直接材质替换
通过识别模型中的特定材质（如地面材质），直接替换为平面图纹理。

```typescript
// 在 floorPlanConfigs 中配置
{
  modelName: '01shuzihuazhuangbeidalou-1030',
  texturePath: '/img/building1-floorplan.jpg',
  materialName: 'FloorMaterial', // 指定要替换的材质名称
  uvScale: { x: 1, y: 1 },
  uvOffset: { x: 0, y: 0 }
}
```

### 方法2：创建内部平面
在模型内部创建新的平面几何体，并应用平面图纹理。

```typescript
// 自动在模型底部创建平面图平面
const floorPlan = createFloorPlanPlane(
  texture,
  new THREE.Vector3(center.x, box.min.y + 0.1, center.z),
  new THREE.Euler(-Math.PI / 2, 0, 0),
  { width: size.x * 0.8, height: size.z * 0.8 }
);
```

## 配置步骤

### 1. 准备平面图图片
将平面图图片放置在 `public/img/` 目录下，支持格式：
- JPG/JPEG
- PNG
- WebP

### 2. 配置模型映射
在 `floorPlanConfigs` 数组中添加配置：

```typescript
const floorPlanConfigs: FloorPlanConfig[] = [
  {
    modelName: '01shuzihuazhuangbeidalou-1030', // 模型文件名（不含.glb）
    texturePath: '/img/building1-floorplan.jpg', // 平面图路径
    materialName: 'FloorMaterial', // 可选：指定材质名称
    uvScale: { x: 1, y: 1 }, // UV缩放
    uvOffset: { x: 0, y: 0 } // UV偏移
  }
];
```

### 3. 自动检测内部表面
系统会自动检测以下命名模式的材质/网格：
- 包含 'floor'、'ground'、'interior' 的英文名称
- 包含 '内部'、'地面' 的中文名称

## 高级配置

### UV映射调整
```typescript
{
  modelName: 'your-model',
  texturePath: '/img/floorplan.jpg',
  uvScale: { x: 2, y: 2 }, // 纹理重复2次
  uvOffset: { x: 0.5, y: 0.5 } // 纹理偏移50%
}
```

### 多层平面图
可以为同一个模型添加多个平面图（不同楼层）：

```typescript
// 在 addInteriorFloorPlan 函数中添加多层支持
const floors = [
  { y: box.min.y + 0.1, texture: floorPlan1 }, // 一楼
  { y: box.min.y + 10, texture: floorPlan2 },  // 二楼
  { y: box.min.y + 20, texture: floorPlan3 }   // 三楼
];
```

## 纹理优化

### 1. 纹理设置
```typescript
loadedTexture.wrapS = THREE.RepeatWrapping;
loadedTexture.wrapT = THREE.RepeatWrapping;
loadedTexture.flipY = false; // GLB模型通常需要设置为false
loadedTexture.minFilter = THREE.LinearFilter;
loadedTexture.magFilter = THREE.LinearFilter;
```

### 2. 材质设置
```typescript
const material = new THREE.MeshStandardMaterial({
  map: texture,
  side: THREE.DoubleSide, // 双面渲染
  transparent: true,
  opacity: 0.9, // 半透明效果
  alphaTest: 0.1 // 透明度测试
});
```

## 使用示例

### 完整配置示例
```typescript
const floorPlanConfigs: FloorPlanConfig[] = [
  {
    modelName: '01shuzihuazhuangbeidalou-1030',
    texturePath: '/img/digital-building-floorplan.jpg',
    uvScale: { x: 1, y: 1 },
    uvOffset: { x: 0, y: 0 }
  },
  {
    modelName: '03xingzhengbangonglou-1030',
    texturePath: '/img/admin-building-floorplan.jpg',
    uvScale: { x: 1.5, y: 1.5 },
    uvOffset: { x: 0, y: 0 }
  },
  {
    modelName: '06shitang-1030',
    texturePath: '/img/canteen-floorplan.jpg',
    materialName: 'CanteenFloor',
    uvScale: { x: 1, y: 1 },
    uvOffset: { x: 0, y: 0 }
  }
];
```

## 调试技巧

### 1. 查看模型结构
```typescript
// 在模型加载后打印结构信息
gltf.scene.traverse((child) => {
  if (child instanceof THREE.Mesh) {
    console.log('Mesh name:', child.name);
    console.log('Material name:', (child.material as any).name);
    console.log('Material type:', child.material.type);
  }
});
```

### 2. 可视化边界盒
```typescript
// 添加边界盒辅助器
const box = new THREE.Box3().setFromObject(model);
const helper = new THREE.Box3Helper(box, 0xffff00);
scene.add(helper);
```

### 3. 纹理加载状态
```typescript
textureLoader.load(
  texturePath,
  (texture) => console.log('Texture loaded successfully'),
  (progress) => console.log('Loading progress:', progress),
  (error) => console.error('Texture loading error:', error)
);
```

## 注意事项

1. **文件路径**：确保平面图图片路径正确，相对于public目录
2. **UV坐标**：GLB模型的UV坐标可能需要调整flipY设置
3. **性能**：大量纹理会影响性能，考虑使用纹理压缩
4. **材质名称**：不同的3D建模软件导出的材质名称可能不同
5. **坐标系**：注意Three.js的坐标系统（Y轴向上）

## 扩展功能

### 交互式平面图切换
可以添加UI控件来动态切换不同楼层的平面图：

```typescript
const switchFloorPlan = (modelName: string, floorLevel: number) => {
  // 实现楼层切换逻辑
};
```

### 平面图热点
在平面图上添加可点击的热点区域：

```typescript
const addHotspots = (floorPlanMesh: THREE.Mesh) => {
  // 添加交互热点
};
```
